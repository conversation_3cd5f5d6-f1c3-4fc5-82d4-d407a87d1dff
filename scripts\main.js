// Digital Portfolio System - Main JavaScript

// DOM Elements (will be initialized after DOM loads)
let navToggle, navMenu, authModal, loginForm, registerForm;

// Initialize on DOM load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DOM elements
    navToggle = document.getElementById('nav-toggle');
    navMenu = document.getElementById('nav-menu');
    authModal = document.getElementById('authModal');
    loginForm = document.getElementById('loginForm');
    registerForm = document.getElementById('registerForm');

    initializeApp();
    setupEventListeners();
    setupAnimations();
});

function initializeApp() {
    console.log('PortfolioPro System initialized');

    // Check login status and handle redirections
    checkLoginStatus();

    // Load saved theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }

    // Initialize tooltips and other UI components
    initializeTooltips();

    // Setup intersection observer for animations
    setupIntersectionObserver();

    // Update UI based on login status
    updateUIForLoginStatus();
}

function setupEventListeners() {
    // Navigation Toggle
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }

    // User Type Button Selection
    setupUserTypeButtons();

    // Smooth Scrolling for Navigation Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');

            // If href is just '#', prevent default behavior (page jump)
            // but do not attempt querySelector as it's not a scroll target.
            if (href === '#') {
                e.preventDefault(); 
                return; 
            }

            // For actual scroll links, prevent default and perform smooth scroll
            e.preventDefault();
            const target = document.querySelector(href);
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Header Scroll Effect
    window.addEventListener('scroll', throttle(() => {
        const header = document.querySelector('.header');
        if (header) {
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        }
    }, 10));

    // Search Functionality
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');

    if (searchBtn && searchInput) {
        searchBtn.addEventListener('click', () => {
            const query = searchInput.value.trim();
            if (query) {
                performSearch(query);
            }
        });

        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const query = searchInput.value.trim();
                if (query) {
                    performSearch(query);
                }
            }
        });
    }

    // Tag Click Functionality
    document.querySelectorAll('.tag').forEach(tag => {
        tag.addEventListener('click', () => {
            const tagText = tag.textContent;
            if (searchInput) {
                searchInput.value = tagText;
                performSearch(tagText);
            }
        });
    });

    // Service Card Interactions
    document.querySelectorAll('.service-card').forEach(card => {
        card.addEventListener('click', () => {
            const serviceName = card.querySelector('h3').textContent;
            console.log('Service clicked:', serviceName);
            showNotification(`Exploring ${serviceName} services...`, 'info');
        });
    });

    // Freelancer Card Interactions
    document.querySelectorAll('.freelancer-card').forEach(card => {
        card.addEventListener('click', (e) => {
            // Don't trigger if clicking on action buttons
            if (e.target.closest('.freelancer-actions')) return;

            const freelancerName = card.querySelector('h3').textContent;
            console.log('Freelancer clicked:', freelancerName);
            viewProfile(freelancerName.toLowerCase().replace(' ', '-'));
        });
    });

    // Form Submissions - This event listener will now call specific handlers
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', (e) => {
            e.preventDefault(); // Prevent default form submission

            const formType = getFormType(form);
            switch (formType) {
                case 'login':
                    handleLogin(form);
                    break;
                case 'register':
                    handleRegistration(form);
                    break;
                case 'contact':
                    showNotification('Message sent successfully!', 'success');
                    closeModal('contactModal');
                    break;
                case 'project':
                    showNotification('Project posted successfully!', 'success');
                    closeModal('projectModal');
                    break;
                default:
                    showNotification('Form submitted!', 'success');
                    break;
            }
        });
    });

    // Close modals when clicking outside
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target.id);
        }
    });

    // Escape key to close modals
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal[style*="block"]');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });

    // Add event listeners for tab buttons directly here
    document.querySelectorAll('.tab-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            // Pass the tab name (e.g., from data-tab attribute)
            const tabName = this.getAttribute('data-tab'); // Assuming a data-tab attribute on your buttons
            switchTab(tabName, e); // Pass the event object to switchTab
        });
    });
}

// User Type Button Setup
function setupUserTypeButtons() {
    console.log('Setting up user type buttons...');
    document.addEventListener('click', function(e) {
        if (e.target.closest('.user-type-btn')) {
            const button = e.target.closest('.user-type-btn');
            const userType = button.getAttribute('data-type');
            const form = button.closest('form');

            console.log('User type button clicked:', userType, 'in form:', form.id);

            // Remove active class from all buttons in this form
            form.querySelectorAll('.user-type-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            button.classList.add('active');

            // Set the hidden input value
            const hiddenInput = form.querySelector('input[type="hidden"]');
            if (hiddenInput) {
                hiddenInput.value = userType;
                console.log('Hidden input value set to:', userType);
            } else {
                console.error('Hidden input not found in form:', form.id);
            }

            console.log('Selected user type:', userType);
        }
    });
}

function setupAnimations() {
    // Add stagger animation to grid items
    const gridContainers = document.querySelectorAll('.services-grid, .freelancers-grid, .testimonials-grid');
    gridContainers.forEach(container => {
        container.classList.add('stagger-children');
    });

    // Animate hero elements
    const heroElements = document.querySelectorAll('.hero-title, .hero-description, .hero-search, .hero-tags');
    heroElements.forEach((element, index) => {
        element.style.opacity = '0';
        setTimeout(() => {
            element.style.opacity = '1';
            element.classList.add('animate-fade-in-up');
        }, index * 200);
    });
}

function setupIntersectionObserver() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.service-card, .freelancer-card, .testimonial-card, .stat-card').forEach(el => {
        observer.observe(el);
    });
}

function initializeTooltips() {
    // Simple tooltip implementation
    document.querySelectorAll('[data-tooltip]').forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = e.target.getAttribute('data-tooltip');
    document.body.appendChild(tooltip);

    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
}

function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Auth Modal Functions
function openAuthModal(mode) {
    console.log('Opening auth modal in mode:', mode);

    if (authModal) {
        authModal.style.display = 'block';
        document.body.style.overflow = 'hidden';

        if (mode === 'login') {
            if (loginForm) loginForm.style.display = 'block';
            if (registerForm) registerForm.style.display = 'none';
        } else {
            if (loginForm) loginForm.style.display = 'none';
            if (registerForm) registerForm.style.display = 'block';
        }

        authModal.classList.add('animate-fade-in-up');
    } else {
        console.error('Auth modal not found!');
    }
}

function closeAuthModal() {
    if (authModal) {
        authModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

function switchAuthMode(mode) {
    if (mode === 'login') {
        if (loginForm) loginForm.style.display = 'block';
        if (registerForm) registerForm.style.display = 'none';
    } else {
        if (loginForm) loginForm.style.display = 'none';
        if (registerForm) registerForm.style.display = 'block';
    }
}

// Modal Functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        modal.classList.add('animate-fade-in-up');
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// Search Functions
function performSearch(query) {
    console.log('Searching for:', query);
    showNotification(`Searching for: ${query}`, 'info');

    // Here you would implement actual search functionality with a backend API call
    // For now, we'll simulate a search
    setTimeout(() => {
        showNotification(`Found results for: ${query}`, 'success');
    }, 1000);
}

function searchFreelancers() {
    showNotification('Opening freelancer search...', 'info');
    // Navigate to freelancer search or open search modal
}

// Profile Functions
function viewProfile(profileId) {
    console.log('Viewing profile:', profileId);
    showNotification(`Opening ${profileId}'s profile...`, 'info');
    // Here you would navigate to the profile page
}

function contactFreelancer(freelancerName) {
    console.log('Contacting freelancer:', freelancerName);
    openModal('contactModal');

    // Update modal with freelancer info
    const nameElement = document.getElementById('contactFreelancerName');
    if (nameElement) {
        nameElement.textContent = freelancerName;
    }
}

// --- DEMO LOGIN (No Backend Required) ---
async function handleLogin(form) {
    const emailInput = form.querySelector('#loginEmail');
    const passwordInput = form.querySelector('#loginPassword');

    const email = emailInput ? emailInput.value : '';
    const password = passwordInput ? passwordInput.value : '';

    if (!email || !password) {
        showNotification('Please enter both email and password.', 'error');
        return;
    }

    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn ? submitBtn.textContent : 'Sign In';
    if (submitBtn) {
        submitBtn.textContent = 'Signing In...';
        submitBtn.disabled = true;
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    try {
        // Demo users for testing (you can modify these)
        const demoUsers = {
            '<EMAIL>': {
                email: '<EMAIL>',
                fullName: 'Eleni Berhan',
                role: 'freelancer',
                id: '1'
            },
            '<EMAIL>': {
                email: '<EMAIL>',
                fullName: 'Hawinet Mkeonen',
                role: 'client',
                id: '2'
            },
            '<EMAIL>': {
                email: '<EMAIL>',
                fullName: 'Admin User',
                role: 'admin',
                id: '3'
            }
        };

        // Check if user exists and password is correct (for demo, any password works)
        const user = demoUsers[email.toLowerCase()];

        if (user && password.length >= 3) { // Simple password validation for demo
            showNotification('Sign In Successful!', 'success');
            console.log('Login successful:', user);

            // Store demo token and user info
            localStorage.setItem('token', 'demo-token-' + Date.now());
            localStorage.setItem('user', JSON.stringify(user));

            closeAuthModal();

            setTimeout(() => {
                redirectToDashboard(user.role);
            }, 500);
        } else {
            showNotification('Invalid email or password. Try: <EMAIL>, <EMAIL>, or <EMAIL>', 'error');
        }
    } catch (error) {
        console.error('Error during sign-in:', error);
        showNotification('An error occurred during sign-in.', 'error');
    } finally {
        if (submitBtn) {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }
}

// --- Existing handleRegistration function (no changes needed based on this request) ---
async function handleRegistration(form) {
    const fullNameInput = form.querySelector('#registerName');
    const emailInput = form.querySelector('#registerEmail');
    const passwordInput = form.querySelector('#registerPassword');
    const userTypeInput = form.querySelector('#registerUserType'); // This is the hidden input

    const fullName = fullNameInput ? fullNameInput.value : '';
    const email = emailInput ? emailInput.value : '';
    const password = passwordInput ? passwordInput.value : '';
    const userType = userTypeInput ? userTypeInput.value : '';

    if (!fullName || !email || !password) {
        showNotification('Please fill in all registration fields.', 'error');
        return;
    }

    if (!userType) {
        showNotification('Please select whether you want to hire freelancers or work as a freelancer.', 'error');
        return;
    }

    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn ? submitBtn.textContent : 'Register';
    if (submitBtn) {
        submitBtn.textContent = 'Registering...';
        submitBtn.disabled = true;
    }

    try {
        const response = await fetch('http://localhost:5000/api/users/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ fullName, email, password, userType }),
        });

        const data = await response.json();

        if (response.ok) {
            showNotification('Registration Successful! Please sign in.', 'success');
            console.log('Registration successful:', data);
            // After successful registration, automatically switch to login form
            switchAuthMode('login');
            // Optionally clear the registration form
            form.reset();
        } else {
            showNotification(`Registration Failed: ${data.message || 'Something went wrong during registration.'}`, 'error');
            console.error('Registration failed:', data);
        }
    } catch (error) {
        console.error('Error during registration:', error);
        showNotification('An error occurred during registration. Please try again.', 'error');
    } finally {
        if (submitBtn) {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }
}

function getFormType(form) {
    // Ensure these IDs/classes match your HTML forms
    if (form.id === 'loginFormElement') return 'login';
    if (form.id === 'registerFormElement') return 'register';
    if (form.classList.contains('contact-form')) return 'contact';
    if (form.classList.contains('project-form')) return 'project';
    return 'unknown';
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Tab System
// Modified switchTab to accept event object and use it reliably
function switchTab(tabName, event) { // Pass event as an argument
    // Hide all tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab pane
    const selectedPane = document.getElementById(tabName);
    if (selectedPane) {
        selectedPane.classList.add('active');
    }

    // Add active class to clicked tab button
    // Now 'event' is reliably available here
    const clickedBtn = event ? event.target.closest('.tab-btn') : null; // Safely check for event
    if (clickedBtn) {
        clickedBtn.classList.add('active');
    }
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Theme Toggle
function toggleTheme() {
    document.body.classList.toggle('dark-theme');
    const isDark = document.body.classList.contains('dark-theme');
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
    showNotification(`Switched to ${isDark ? 'dark' : 'light'} theme`, 'info');
}

// Error Handling
window.addEventListener('error', (e) => {
    console.error('JavaScript Error:', e.error);
    showNotification('An unexpected error occurred. Please try again.', 'error');
});

// Login Status Management Functions
function checkLoginStatus() {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    const currentPage = getCurrentPageType();

    console.log('Checking login status...', { token: !!token, user: !!user, currentPage });

    if (token && user) {
        const userData = JSON.parse(user);
        const userRole = userData.role;

        // If user is logged in, check if they're on the right page
        if (currentPage === 'landing') {
            // Redirect logged-in users from landing page to their dashboard
            redirectToDashboard(userRole);
        } else if (currentPage === 'dashboard') {
            // Check if user is on the correct dashboard
            const expectedDashboard = getDashboardTypeFromRole(userRole);
            const currentDashboard = getCurrentDashboardType();

            if (currentDashboard !== expectedDashboard) {
                console.log(`User role ${userRole} doesn't match current dashboard ${currentDashboard}, redirecting...`);
                redirectToDashboard(userRole);
            }
        }
    } else {
        // User is not logged in
        if (currentPage === 'dashboard') {
            // Redirect to landing page if trying to access dashboard without login
            console.log('User not logged in, redirecting to landing page...');
            window.location.href = 'index.html';
        }
    }
}

function getCurrentPageType() {
    const path = window.location.pathname;
    const filename = path.split('/').pop();

    if (filename === 'index.html' || filename === '') {
        return 'landing';
    } else if (filename.includes('dashboard')) {
        return 'dashboard';
    }
    return 'other';
}

function getCurrentDashboardType() {
    const path = window.location.pathname;
    const filename = path.split('/').pop();

    if (filename.includes('freelancer')) return 'freelancer';
    if (filename.includes('client')) return 'client';
    if (filename.includes('admin')) return 'admin';
    return null;
}

function getDashboardTypeFromRole(role) {
    switch (role) {
        case 'freelancer': return 'freelancer';
        case 'client': return 'client';
        case 'admin': return 'admin';
        default: return null;
    }
}

function redirectToDashboard(userRole) {
    console.log('Redirecting to dashboard for role:', userRole);

    setTimeout(() => {
        switch (userRole) {
            case 'freelancer':
                window.location.href = 'freelancer-dashboard.html';
                break;
            case 'client':
                window.location.href = 'client-dashboard.html';
                break;
            case 'admin':
                window.location.href = 'admin-dashboard.html';
                break;
            default:
                console.warn('Unknown user role:', userRole);
                showNotification('Unknown user role. Please contact support.', 'error');
        }
    }, 500);
}

function updateUIForLoginStatus() {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    if (token && user) {
        const userData = JSON.parse(user);
        updateUIForLoggedInUser(userData);
    } else {
        updateUIForLoggedOutUser();
    }
}

function updateUIForLoggedInUser(userData) {
    // Update navigation buttons
    const navActions = document.querySelector('.nav-actions');
    if (navActions) {
        navActions.innerHTML = `
            <div class="user-menu">
                <span class="user-greeting">Hello, ${userData.fullName || userData.name || 'User'}</span>
                <button class="btn btn-outline" onclick="logout()">Logout</button>
                <button class="btn btn-primary" onclick="goToDashboard()">Dashboard</button>
            </div>
        `;
    }

    // Hide auth modal if it's open
    closeAuthModal();
}

function updateUIForLoggedOutUser() {
    // Restore original navigation buttons
    const navActions = document.querySelector('.nav-actions');
    if (navActions) {
        navActions.innerHTML = `
            <button class="btn btn-outline" onclick="openAuthModal('login')">Sign In</button>
            <button class="btn btn-primary" onclick="openAuthModal('register')">Join</button>
            <button class="nav-toggle" id="nav-toggle">
                <i class="fas fa-bars"></i>
            </button>
        `;

        // Re-setup nav toggle after updating HTML
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
        }
    }
}

function logout() {
    console.log('Logging out user...');

    // Clear stored user data
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // Show notification
    showNotification('Logged out successfully!', 'success');

    // Update UI
    updateUIForLoggedOutUser();

    // Redirect to landing page after a short delay
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1000);
}

function goToDashboard() {
    const user = localStorage.getItem('user');
    if (user) {
        const userData = JSON.parse(user);
        redirectToDashboard(userData.role);
    }
}

function isUserLoggedIn() {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    return !!(token && user);
}

function getCurrentUser() {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
}

// Export functions for global use
window.openAuthModal = openAuthModal;
window.closeAuthModal = closeAuthModal;
window.switchAuthMode = switchAuthMode;
window.openModal = openModal;
window.closeModal = closeModal;
window.toggleTheme = toggleTheme;
window.switchTab = switchTab;
window.contactFreelancer = contactFreelancer;
window.viewProfile = viewProfile;
window.searchFreelancers = searchFreelancers;
window.logout = logout;
window.goToDashboard = goToDashboard;
window.isUserLoggedIn = isUserLoggedIn;
window.getCurrentUser = getCurrentUser;

console.log('PortfolioPro System loaded successfully!');