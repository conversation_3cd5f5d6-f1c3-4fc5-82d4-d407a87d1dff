// Freelancer Dashboard JavaScript

// Dashboard Data
const freelancerData = {
    profile: {
        name: '<PERSON><PERSON>',
        title: 'Full Stack Developer & UI/UX Designer',
        bio: 'Passionate full-stack developer with 5+ years of experience creating beautiful, functional web applications.',
        location: 'Addis Ababa, Ethiopia',
        hourlyRate: 75,
        experience: 5
    },
    stats: {
        activeProjects: 12,
        totalEarnings: 4250,
        averageRating: 4.9,
        profileViews: 1234
    },
    skills: [
        { name: 'JavaScript', level: 95 },
        { name: 'React', level: 90 },
        { name: 'Node.js', level: 85 },
        { name: 'UI/UX Design', level: 80 },
        { name: 'Python', level: 75 }
    ],
    portfolio: [
        {
            id: 1,
            title: 'E-commerce Platform',
            description: 'Modern online shopping experience',
            image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
            tags: ['React', 'Node.js', 'MongoDB'],
            category: 'web'
        }
    ],
    calendar: {
        currentMonth: 'December 2024',
        events: [
            {
                id: 1,
                title: 'Client Meeting - Hawinet Mkeonen',
                description: 'Discuss website project requirements',
                date: '2024-12-15',
                time: '14:00',
                type: 'meeting'
            },
            {
                id: 2,
                title: 'Project Deadline - E-commerce Site',
                description: 'Final delivery and testing',
                date: '2024-12-18',
                time: '10:00',
                type: 'deadline'
            }
        ]
    }
};

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeFreelancerDashboard();
    setupFreelancerEventListeners();
    generateCalendar();
    updateDashboardStats();
});

function initializeFreelancerDashboard() {
    console.log('Freelancer Dashboard initialized');
    
    // Animate dashboard cards on load
    const dashboardCards = document.querySelectorAll('.dashboard-card, .stat-card');
    dashboardCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate-fade-in-up');
        }, index * 100);
    });
    
    // Load user data
    loadFreelancerProfile();
}

function setupFreelancerEventListeners() {
    // Profile editing
    setupProfileEditing();
    
    // Calendar functionality
    setupCalendarEvents();
    
    // Skills management
    setupSkillsManagement();
    
    // Portfolio management
    setupPortfolioManagement();
    
    // Export functionality
    setupExportFunctionality();
}

function loadFreelancerProfile() {
    // Load profile data into form fields
    const nameInput = document.querySelector('input[value="Eleni Berhan"]');
    const titleInput = document.querySelector('input[value="Full Stack Developer & UI/UX Designer"]');
    const locationInput = document.querySelector('input[value="Addis Ababa, Ethiopia"]');
    
    if (nameInput) nameInput.value = freelancerData.profile.name;
    if (titleInput) titleInput.value = freelancerData.profile.title;
    if (locationInput) locationInput.value = freelancerData.profile.location;
}

function updateDashboardStats() {
    // Update stats cards with real data
    const stats = freelancerData.stats;
    
    // This would typically fetch from an API
    console.log('Stats updated:', stats);
}

// Profile Management
function setupProfileEditing() {
    // Avatar upload
    const editAvatarBtn = document.querySelector('.edit-avatar-btn');
    if (editAvatarBtn) {
        editAvatarBtn.addEventListener('click', () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = handleAvatarUpload;
            input.click();
        });
    }
    
    // Cover photo upload
    const editCoverBtn = document.querySelector('.edit-cover-btn');
    if (editCoverBtn) {
        editCoverBtn.addEventListener('click', () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = handleCoverUpload;
            input.click();
        });
    }
}

function handleAvatarUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const avatarImg = document.querySelector('.profile-avatar img');
            if (avatarImg) {
                avatarImg.src = e.target.result;
            }
            showNotification('Avatar updated successfully!', 'success');
        };
        reader.readAsDataURL(file);
    }
}

function handleCoverUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const coverDiv = document.querySelector('.cover-image');
            if (coverDiv) {
                coverDiv.style.backgroundImage = `url(${e.target.result})`;
            }
            showNotification('Cover photo updated successfully!', 'success');
        };
        reader.readAsDataURL(file);
    }
}

// Skills Management
function setupSkillsManagement() {
    // Skill range sliders
    document.querySelectorAll('.skill-range').forEach(slider => {
        slider.addEventListener('input', function() {
            const percentage = this.nextElementSibling;
            if (percentage) {
                percentage.textContent = this.value + '%';
            }
        });
    });
    
    // Remove skill buttons
    document.querySelectorAll('.btn-remove').forEach(btn => {
        btn.addEventListener('click', function() {
            this.closest('.skill-edit-item').remove();
            showNotification('Skill removed', 'info');
        });
    });
}

function addSkill() {
    const skillsList = document.querySelector('.skills-edit-list');
    if (skillsList) {
        const newSkillItem = document.createElement('div');
        newSkillItem.className = 'skill-edit-item';
        newSkillItem.innerHTML = `
            <input type="text" class="form-input" placeholder="Skill name">
            <input type="range" class="skill-range" min="0" max="100" value="50">
            <span class="skill-percentage">50%</span>
            <button class="btn-remove" onclick="this.closest('.skill-edit-item').remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        skillsList.appendChild(newSkillItem);
        
        // Setup event listeners for new skill
        const newSlider = newSkillItem.querySelector('.skill-range');
        newSlider.addEventListener('input', function() {
            const percentage = this.nextElementSibling;
            percentage.textContent = this.value + '%';
        });
        
        showNotification('New skill added', 'success');
    }
}

// Portfolio Management
function setupPortfolioManagement() {
    // Portfolio image upload
    document.querySelectorAll('.upload-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (e) => handlePortfolioImageUpload(e, this);
            input.click();
        });
    });
}

function handlePortfolioImageUpload(event, button) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = button.closest('.portfolio-image-upload').querySelector('img');
            if (img) {
                img.src = e.target.result;
            }
            showNotification('Portfolio image updated!', 'success');
        };
        reader.readAsDataURL(file);
    }
}

function addPortfolioItem() {
    const portfolioGrid = document.querySelector('.portfolio-edit-grid');
    if (portfolioGrid) {
        const newItem = document.createElement('div');
        newItem.className = 'portfolio-edit-item';
        newItem.innerHTML = `
            <div class="portfolio-image-upload">
                <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop" alt="New Project">
                <div class="upload-overlay">
                    <button class="upload-btn">
                        <i class="fas fa-camera"></i>
                        Change Image
                    </button>
                </div>
            </div>
            <div class="portfolio-edit-info">
                <input type="text" class="form-input" placeholder="Project Title">
                <textarea class="form-input" rows="2" placeholder="Project Description"></textarea>
                <div class="portfolio-tags-edit">
                    <input type="text" class="form-input" placeholder="Add tags (comma separated)">
                </div>
                <div class="portfolio-actions">
                    <button class="btn btn-outline btn-sm">
                        <i class="fas fa-save"></i>
                        Save
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="this.closest('.portfolio-edit-item').remove()">
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                </div>
            </div>
        `;
        
        portfolioGrid.appendChild(newItem);
        
        // Setup upload functionality for new item
        const uploadBtn = newItem.querySelector('.upload-btn');
        uploadBtn.addEventListener('click', function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (e) => handlePortfolioImageUpload(e, this);
            input.click();
        });
        
        showNotification('New portfolio item added', 'success');
    }
}

// Calendar Management
function setupCalendarEvents() {
    // Calendar navigation
    const prevBtn = document.querySelector('.calendar-controls .btn:first-child');
    const nextBtn = document.querySelector('.calendar-controls .btn:last-child');
    
    if (prevBtn) prevBtn.addEventListener('click', previousMonth);
    if (nextBtn) nextBtn.addEventListener('click', nextMonth);
}

function generateCalendar() {
    const calendarDays = document.getElementById('calendarDays');
    if (!calendarDays) return;
    
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Clear existing days
    calendarDays.innerHTML = '';
    
    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    
    // Add empty cells for days before month starts
    for (let i = 0; i < firstDay; i++) {
        const emptyDay = document.createElement('div');
        emptyDay.className = 'calendar-day empty';
        calendarDays.appendChild(emptyDay);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        dayElement.textContent = day;
        
        // Check if this day has events
        const hasEvent = freelancerData.calendar.events.some(event => {
            const eventDate = new Date(event.date);
            return eventDate.getDate() === day && eventDate.getMonth() === month;
        });
        
        if (hasEvent) {
            dayElement.classList.add('has-event');
        }
        
        // Mark today
        if (day === currentDate.getDate() && month === currentDate.getMonth()) {
            dayElement.classList.add('today');
        }
        
        dayElement.addEventListener('click', () => selectDate(year, month, day));
        calendarDays.appendChild(dayElement);
    }
}

function previousMonth() {
    // Implementation for previous month navigation
    showNotification('Previous month', 'info');
}

function nextMonth() {
    // Implementation for next month navigation
    showNotification('Next month', 'info');
}

function selectDate(year, month, day) {
    console.log('Selected date:', year, month + 1, day);
    showNotification(`Selected: ${month + 1}/${day}/${year}`, 'info');
}

function toggleCalendarView() {
    showNotification('Calendar view toggled', 'info');
}

// Export Functionality
function setupExportFunctionality() {
    // Export buttons would be set up here
}

function exportPortfolio() {
    openModal('exportModal');
}

function exportToPDF() {
    showNotification('Generating PDF...', 'info');
    
    // Simulate PDF generation
    setTimeout(() => {
        showNotification('PDF exported successfully!', 'success');
        closeModal('exportModal');
    }, 2000);
}

function exportToZIP() {
    showNotification('Creating ZIP archive...', 'info');
    
    // Simulate ZIP creation
    setTimeout(() => {
        showNotification('ZIP archive created successfully!', 'success');
        closeModal('exportModal');
    }, 3000);
}

function generateShareLink() {
    const shareLink = `https://portfoliopro.com/profile/${freelancerData.profile.name.toLowerCase().replace(' ', '-')}`;
    
    // Copy to clipboard
    navigator.clipboard.writeText(shareLink).then(() => {
        showNotification('Share link copied to clipboard!', 'success');
    }).catch(() => {
        showNotification('Share link: ' + shareLink, 'info');
    });
    
    closeModal('exportModal');
}

// Certification Management
function addCertification() {
    showNotification('Add certification functionality would open here', 'info');
}

// Analytics Functions
function updateAnalytics() {
    // Update analytics data
    console.log('Analytics updated');
}

// Notification function (if not already defined in main.js)
if (typeof showNotification === 'undefined') {
    function showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Project Search and Application Functions
function applyToProject(projectId) {
    console.log('Applying to project:', projectId);
    openModal('projectApplicationModal');

    // Update modal with project info based on projectId
    const projectTitles = {
        'ecommerce-website': 'E-commerce Website Development',
        'mobile-app-design': 'Mobile App UI/UX Design',
        'marketing-campaign': 'Digital Marketing Campaign'
    };

    const projectBudgets = {
        'ecommerce-website': '$2,500 - $4,000',
        'mobile-app-design': '$1,200 - $2,000',
        'marketing-campaign': '$800 - $1,500'
    };

    const titleElement = document.getElementById('applicationProjectTitle');
    const budgetElement = document.getElementById('applicationProjectBudget');

    if (titleElement) titleElement.textContent = projectTitles[projectId] || 'Project Application';
    if (budgetElement) budgetElement.textContent = projectBudgets[projectId] || 'Budget TBD';
}

function setupProjectSearch() {
    // Project search form
    const searchForm = document.querySelector('.project-search-filters');
    if (searchForm) {
        const searchBtn = searchForm.querySelector('.btn-primary');
        if (searchBtn) {
            searchBtn.addEventListener('click', performProjectSearch);
        }

        const clearBtn = searchForm.querySelector('.btn-outline');
        if (clearBtn) {
            clearBtn.addEventListener('click', clearProjectFilters);
        }
    }

    // Sort functionality
    const sortSelect = document.querySelector('.project-search-results .sort-options select');
    if (sortSelect) {
        sortSelect.addEventListener('change', sortProjects);
    }

    // Project application form
    const applicationForm = document.querySelector('.project-application-form');
    if (applicationForm) {
        applicationForm.addEventListener('submit', handleProjectApplication);
    }
}

function performProjectSearch() {
    const searchData = {
        query: document.querySelector('.project-search-filters input[type="text"]').value,
        category: document.querySelector('.project-search-filters select:nth-child(1)').value,
        budget: document.querySelector('.project-search-filters select:nth-child(2)').value,
        duration: document.querySelector('.project-search-filters select:nth-child(3)').value,
        experience: document.querySelector('.project-search-filters select:nth-child(4)').value,
        posted: document.querySelector('.project-search-filters select:nth-child(5)').value
    };

    console.log('Project search:', searchData);
    showNotification('Searching for projects...', 'info');

    // Simulate search results
    setTimeout(() => {
        showNotification('Found 18 projects matching your criteria', 'success');
    }, 1500);
}

function clearProjectFilters() {
    document.querySelectorAll('.project-search-filters input, .project-search-filters select').forEach(input => {
        input.value = '';
    });
    showNotification('Search filters cleared', 'info');
}

function sortProjects() {
    const sortBy = document.querySelector('.project-search-results .sort-options select').value;
    console.log('Sorting projects by:', sortBy);
    showNotification(`Sorted by ${sortBy}`, 'info');
}

function handleProjectApplication(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const applicationData = Object.fromEntries(formData);

    console.log('Project application submitted:', applicationData);

    showNotification('Submitting application...', 'info');

    setTimeout(() => {
        showNotification('Application submitted successfully!', 'success');
        closeModal('projectApplicationModal');
        e.target.reset();
    }, 2000);
}

// Update the initialization function to include project search
function initializeFreelancerDashboard() {
    console.log('Freelancer Dashboard initialized');

    // Animate dashboard cards on load
    const dashboardCards = document.querySelectorAll('.dashboard-card, .stat-card');
    dashboardCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate-fade-in-up');
        }, index * 100);
    });

    // Load user data
    loadFreelancerProfile();

    // Setup project search functionality
    setupProjectSearch();
}

// Search Sub-tabs Functions
function switchSearchTab(tabName) {
    // Hide all search sub-panes
    document.querySelectorAll('.search-sub-pane').forEach(pane => {
        pane.classList.remove('active');
    });

    // Remove active class from all sub-tab buttons
    document.querySelectorAll('.sub-tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected sub-pane
    const selectedPane = document.getElementById(tabName + '-search');
    if (selectedPane) {
        selectedPane.classList.add('active');
    }

    // Add active class to clicked sub-tab button
    const clickedBtn = event.target.closest('.sub-tab-btn');
    if (clickedBtn) {
        clickedBtn.classList.add('active');
    }

    console.log('Switched to search tab:', tabName);
}

// Client Contact Functions
function contactClient(clientId) {
    console.log('Contacting client:', clientId);

    const clientNames = {
        'techstart-inc': 'TechStart Inc.',
        'creative-agency-pro': 'Creative Agency Pro'
    };

    const clientName = clientNames[clientId] || 'Client';

    // Open contact modal (reuse existing contact modal)
    openModal('contactModal');

    // Update modal with client info
    const nameElement = document.getElementById('contactFreelancerName');
    if (nameElement) {
        nameElement.textContent = clientName;
    }

    showNotification(`Opening contact form for ${clientName}`, 'info');
}

function setupClientSearch() {
    // Client search form
    const clientSearchForm = document.querySelector('.client-search-filters');
    if (clientSearchForm) {
        const searchBtn = clientSearchForm.querySelector('.btn-primary');
        if (searchBtn) {
            searchBtn.addEventListener('click', performClientSearch);
        }

        const clearBtn = clientSearchForm.querySelector('.btn-outline');
        if (clearBtn) {
            clearBtn.addEventListener('click', clearClientFilters);
        }
    }

    // Client sort functionality
    const clientSortSelect = document.querySelector('.client-search-results .sort-options select');
    if (clientSortSelect) {
        clientSortSelect.addEventListener('change', sortClients);
    }
}

function performClientSearch() {
    const searchData = {
        query: document.querySelector('.client-search-filters input[type="text"]').value,
        industry: document.querySelector('.client-search-filters select:nth-child(1)').value,
        companySize: document.querySelector('.client-search-filters select:nth-child(2)').value,
        location: document.querySelector('.client-search-filters select:nth-child(3)').value
    };

    console.log('Client search:', searchData);
    showNotification('Searching for clients...', 'info');

    // Simulate search results
    setTimeout(() => {
        showNotification('Found 12 potential clients matching your criteria', 'success');
    }, 1500);
}

function clearClientFilters() {
    document.querySelectorAll('.client-search-filters input, .client-search-filters select').forEach(input => {
        input.value = '';
    });
    showNotification('Client search filters cleared', 'info');
}

function sortClients() {
    const sortBy = document.querySelector('.client-search-results .sort-options select').value;
    console.log('Sorting clients by:', sortBy);
    showNotification(`Sorted clients by ${sortBy}`, 'info');
}

// Update the initialization function to include client search
function initializeFreelancerDashboard() {
    console.log('Freelancer Dashboard initialized');

    // Animate dashboard cards on load
    const dashboardCards = document.querySelectorAll('.dashboard-card, .stat-card');
    dashboardCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate-fade-in-up');
        }, index * 100);
    });

    // Load user data
    loadFreelancerProfile();

    // Setup project search functionality
    setupProjectSearch();

    // Setup client search functionality
    setupClientSearch();
}

// Export functions for global use
window.addSkill = addSkill;
window.addPortfolioItem = addPortfolioItem;
window.addCertification = addCertification;
window.exportPortfolio = exportPortfolio;
window.exportToPDF = exportToPDF;
window.exportToZIP = exportToZIP;
window.generateShareLink = generateShareLink;
window.previousMonth = previousMonth;
window.nextMonth = nextMonth;
window.toggleCalendarView = toggleCalendarView;
window.applyToProject = applyToProject;
window.switchSearchTab = switchSearchTab;
window.contactClient = contactClient;

console.log('Freelancer Dashboard JavaScript loaded successfully!');
