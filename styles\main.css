/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Modern CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Root Variables - Clean Professional Palette */
:root {
  /* Professional Color Palette */
  --primary-50: #9ebddd;
  --primary-100: #8bb0d4;
  --primary-200: #a1bcdf;
  --primary-300: #a0bde0;
  --primary-400: #94a3b8;
  --primary-500: #64748b;
  --primary-600: #475569;
  --primary-700: #334155;
  --primary-800: #1e293b;
  --primary-900: #0f172a;

  --accent-50: #748dad;
  --accent-100: #677991;
  --accent-200: #8194ac;
  --accent-300: #93c5fd;
  --accent-400: #60a5fa;
  --accent-500: #3b82f6;
  --accent-600: #2563eb;
  --accent-700: #1d4ed8;
  --accent-800: #1e40af;
  --accent-900: #1e3a8a;

  --success-500: #22c55e;
  --success-600: #16a34a;
  --warning-500: #f59e0b;
  --error-500: #ef4444;

  /* Clean Gradients */
  --gradient-primary: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --gradient-accent: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  --gradient-hero: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);

  /* Professional Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Clean Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(0, 0, 0, 0.1);
  --glass-backdrop: blur(20px);
}

/* Dark Modern Body Styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #f8fafc;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  background-attachment: fixed;
  overflow-x: hidden;
}

/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.text-gradient {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Layout Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.nav-brand h2 {
  font-size: 1.5rem;
  margin: 0;
}

.nav-menu {
  display: flex;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: #f8fafc;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: var(--accent-400);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-toggle {
  display: none;
  background: none;
  border: none;
  color: #f8fafc;
  font-size: 1.5rem;
  cursor: pointer;
}

/* Hero Section */
.hero {
  padding: 8rem 0 4rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-description {
  font-size: 1.25rem;
  color: #cbd5e1;
  margin-bottom: 2rem;
}

.hero-search {
  margin-bottom: 2rem;
}

.search-container {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50px;
  padding: 0.5rem;
  box-shadow: var(--shadow-xl);
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  background: transparent;
  color: #1e293b;
}

.search-btn {
  background: var(--accent-600);
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: var(--accent-700);
  transform: scale(1.05);
}

.hero-tags {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  font-size: 0.875rem;
  color: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tag:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-2xl);
}

.card-icon {
  font-size: 3rem;
  color: var(--accent-400);
  margin-bottom: 1rem;
}

/* Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  font-size: 0.875rem;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  border-radius: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--accent-600);
  color: white;
  box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
  background: var(--accent-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: white;
  text-decoration: none;
}

.btn-outline {
  background: transparent;
  color: var(--accent-600);
  border: 2px solid var(--accent-600);
}

.btn-outline:hover {
  background: var(--accent-600);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

.btn-success {
  background: var(--success-500);
  color: white;
}

.btn-success:hover {
  background: var(--success-600);
  color: white;
  text-decoration: none;
}

.btn-danger {
  background: var(--error-500);
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
  color: white;
  text-decoration: none;
}

.btn-warning {
  background: var(--warning-500);
  color: white;
}

.btn-warning:hover {
  background: #d97706;
  color: white;
  text-decoration: none;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.625rem;
}

/* Section Styles */
.py-20 {
  padding: 5rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.125rem;
  color: #cbd5e1;
}

.text-center {
  text-align: center;
}

.mb-12 {
  margin-bottom: 3rem;
}

.w-full {
  width: 100%;
}

.mt-4 {
  margin-top: 1rem;
}

/* Services Section */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.service-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: rgba(255, 255, 255, 0.2);
}

.service-icon {
  font-size: 3rem;
  color: var(--accent-400);
  margin-bottom: 1rem;
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.service-card p {
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.service-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-weight: 600;
  color: var(--accent-400);
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #fbbf24;
}

/* Freelancers Section */
.freelancers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.freelancer-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.freelancer-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.freelancer-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
}

.freelancer-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.online-status {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 16px;
  height: 16px;
  background: var(--success-500);
  border: 2px solid white;
  border-radius: 50%;
}

.freelancer-info {
  text-align: center;
}

.freelancer-info h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.freelancer-title {
  color: #cbd5e1;
  margin-bottom: 1rem;
}

.freelancer-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.stars {
  display: flex;
  gap: 0.125rem;
  color: #fbbf24;
}

.freelancer-skills {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.skill-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-300);
  border-radius: 12px;
  font-size: 0.75rem;
}

.freelancer-price {
  font-weight: 600;
  color: var(--accent-400);
  margin-bottom: 1rem;
}

.freelancer-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* Freelancer Search */
.freelancer-search-bar {
  margin-bottom: 2rem;
  padding: 2rem;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
}

.search-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.freelancer-search-input {
  flex: 1;
  min-width: 300px;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: #f8fafc;
  font-size: 1rem;
}

.freelancer-search-input::placeholder {
  color: #cbd5e1;
}

.filter-select {
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: #f8fafc;
  font-size: 0.875rem;
}

/* Glass Morphism Components */
.glass-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

/* Testimonials Section */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.testimonial-content {
  margin-bottom: 1.5rem;
}

.quote-icon {
  font-size: 2rem;
  color: var(--accent-600);
  margin-bottom: 1rem;
}

.testimonial-content p {
  color: #475569;
  font-style: italic;
  font-size: 1.125rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h4 {
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.author-info p {
  color: #64748b;
  font-size: 0.875rem;
}

/* Footer */
.footer {
  background: var(--primary-900);
  padding: 4rem 0 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: #cbd5e1;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--accent-400);
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #f8fafc;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--accent-600);
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #cbd5e1;
}

/* Quick Access Panel */
.quick-access-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1rem;
  box-shadow: var(--shadow-xl);
}

.quick-access-content h4 {
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  color: #cbd5e1;
}

.quick-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quick-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  color: #f8fafc;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.quick-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--accent-400);
  text-decoration: none;
}

.quick-link i {
  width: 16px;
  text-align: center;
}

/* Floating Home Button */
.floating-home-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.floating-home-btn .btn {
  border-radius: 50px;
  box-shadow: var(--shadow-xl);
}

/* Dashboard Styles */
.dashboard-main {
  padding-top: 6rem;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2rem 0;
}

.welcome-section h1 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.welcome-section p {
  color: #cbd5e1;
  font-size: 1.125rem;
}

.quick-actions {
  display: flex;
  gap: 1rem;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-menu:hover {
  background: rgba(255, 255, 255, 0.15);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  color: #f8fafc;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: var(--accent-600);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-content h3 {
  font-size: 2rem;
  margin-bottom: 0.25rem;
}

.stat-content p {
  color: #cbd5e1;
  margin-bottom: 0.5rem;
}

.stat-change {
  font-size: 0.875rem;
  font-weight: 500;
}

.stat-change.positive {
  color: var(--success-500);
}

.stat-change.negative {
  color: var(--error-500);
}

.stat-change.warning {
  color: var(--warning-500);
}

/* Dashboard Tabs */
.dashboard-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  padding: 0.5rem;
  background: rgba(30, 41, 59, 0.8);
  border-radius: 16px;
  overflow-x: auto;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: #cbd5e1;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab-btn:hover,
.tab-btn.active {
  background: var(--accent-600);
  color: white;
}

/* Tab Content */
.tab-content {
  position: relative;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.dashboard-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  overflow: hidden;
}

.dashboard-card.full-width {
  grid-column: 1 / -1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
  font-size: 1.25rem;
  margin: 0;
}

.view-all {
  color: var(--accent-400);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.view-all:hover {
  color: var(--accent-300);
}

.card-body {
  padding: 1.5rem;
}

/* Project List */
.project-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.project-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.project-info h4 {
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.project-info p {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.project-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.project-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.project-status.in-progress {
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-300);
}

.project-status.completed {
  background: rgba(34, 197, 94, 0.2);
  color: var(--success-500);
}

.project-status.pending {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-500);
}

.project-deadline,
.freelancer-name {
  font-size: 0.75rem;
  color: #94a3b8;
}

.project-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--accent-400);
}

/* Message List */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
}

.message-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-content h4 {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.message-content p {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-time {
  font-size: 0.75rem;
  color: #94a3b8;
}

.message-status.unread {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 8px;
  height: 8px;
  background: var(--accent-500);
  border-radius: 50%;
}

/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #f8fafc;
  font-weight: 500;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(30, 41, 59, 0.8);
  color: #f8fafc;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-600);
}

.form-input::placeholder {
  color: #94a3b8;
}

/* Ensure auth form elements are visible */
.auth-form .form-group label {
  color: #f8fafc !important;
  font-weight: 600;
}

.auth-form .form-input {
  background: rgba(30, 41, 59, 0.9) !important;
  color: #f8fafc !important;
  border: 2px solid rgba(59, 130, 246, 0.3) !important;
}

.auth-form .form-input:focus {
  border-color: var(--accent-600) !important;
  background: rgba(30, 41, 59, 1) !important;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px);
  margin: 5% auto;
  padding: 2rem;
  border-radius: 24px;
  width: 90%;
  max-width: 500px;
  position: relative;
  box-shadow: var(--shadow-2xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-content.large {
  max-width: 800px;
}

.close {
  position: absolute;
  right: 1rem;
  top: 1rem;
  font-size: 2rem;
  cursor: pointer;
  color: #cbd5e1;
  transition: color 0.3s ease;
}

.close:hover {
  color: #f8fafc;
}

.modal-content h2 {
  color: #f8fafc;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Auth Form Styles */
.auth-form {
  width: 100%;
  max-width: 400px;
}

.auth-form h2 {
  color: #f8fafc;
  margin-bottom: 2rem;
  text-align: center;
  font-size: 1.75rem;
}

.auth-switch {
  text-align: center;
  margin-top: 1.5rem;
  color: #cbd5e1;
}

.auth-switch a {
  color: var(--accent-400);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.auth-switch a:hover {
  color: var(--accent-300);
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow: var(--shadow-xl);
  animation: slideInDown 0.3s ease-out;
}

.notification-info {
  background: var(--accent-600);
  color: white;
}

.notification-success {
  background: var(--success-500);
  color: white;
}

.notification-error {
  background: var(--error-500);
  color: white;
}

.notification-warning {
  background: var(--warning-500);
  color: white;
}

.notification-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.notification-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.notification-close:hover {
  opacity: 1;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Calendar Styles */
.calendar-section {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.calendar-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.calendar-controls h3 {
  margin: 0;
  font-size: 1.5rem;
}

.calendar-actions {
  display: flex;
  gap: 1rem;
}

.calendar-grid {
  margin-bottom: 2rem;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1px;
}

.weekday {
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  color: #cbd5e1;
  background: rgba(255, 255, 255, 0.05);
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
}

.calendar-day {
  aspect-ratio: 1;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.calendar-day:hover {
  background: rgba(255, 255, 255, 0.1);
}

.calendar-day.today {
  background: var(--accent-600);
  color: white;
}

.calendar-day.has-event {
  background: rgba(59, 130, 246, 0.2);
}

/* Events */
.upcoming-events h4 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.event-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.event-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.event-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}

.event-date {
  font-weight: 600;
  color: var(--accent-400);
}

.event-hour {
  font-size: 0.875rem;
  color: #94a3b8;
}

.event-info {
  flex: 1;
}

.event-info h5 {
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.event-info p {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.event-type {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.event-type.meeting {
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-300);
}

.event-type.deadline {
  background: rgba(239, 68, 68, 0.2);
  color: var(--error-500);
}

.event-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Skills & Certification Styles */
.skills-certification-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.skills-tests-card,
.certifications-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  overflow: hidden;
}

.skill-tests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.skill-test-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.skill-test-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.skill-test-item.completed {
  border: 1px solid var(--success-500);
}

.test-icon {
  width: 60px;
  height: 60px;
  background: var(--accent-600);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.test-info {
  flex: 1;
}

.test-info h4 {
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
}

.test-info p {
  color: #cbd5e1;
  margin-bottom: 0.75rem;
}

.test-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.test-duration {
  font-size: 0.875rem;
  color: #94a3b8;
}

.test-difficulty {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.test-difficulty.easy {
  background: rgba(34, 197, 94, 0.2);
  color: var(--success-500);
}

.test-difficulty.intermediate {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-500);
}

.test-difficulty.advanced {
  background: rgba(239, 68, 68, 0.2);
  color: var(--error-500);
}

.test-score {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--success-500);
  font-weight: 600;
  margin-top: 0.5rem;
}

/* Certifications */
.certifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.certification-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.certification-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.cert-badge {
  width: 60px;
  height: 60px;
  background: var(--warning-500);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.cert-info {
  flex: 1;
}

.cert-info h4 {
  margin-bottom: 0.25rem;
  font-size: 1.125rem;
}

.cert-info p {
  color: #cbd5e1;
  margin-bottom: 0.5rem;
}

.cert-meta {
  display: flex;
  gap: 1rem;
}

.cert-date,
.cert-expiry {
  font-size: 0.875rem;
  color: #94a3b8;
}

.cert-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Analytics Styles */
.analytics-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.analytics-overview,
.visitor-analytics,
.popular-projects {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  overflow: hidden;
}

.analytics-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.analytics-stat {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.analytics-stat .stat-icon {
  width: 50px;
  height: 50px;
  background: var(--accent-600);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.analytics-stat .stat-info h4 {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.analytics-stat .stat-info p {
  color: #cbd5e1;
  margin-bottom: 0.25rem;
}

.date-range-selector select {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #f8fafc;
}

/* Chart Styles */
.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 250px;
  padding: 1rem 0;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, var(--accent-600), var(--accent-400));
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  background: linear-gradient(to top, var(--accent-700), var(--accent-500));
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  padding: 0 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

/* Popular Projects */
.popular-projects-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.popular-project-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.popular-project-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.project-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.project-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.project-info {
  flex: 1;
}

.project-info h4 {
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.project-info p {
  color: #cbd5e1;
  font-size: 0.875rem;
}

.project-stats {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-end;
}

.view-count,
.click-rate {
  font-size: 0.875rem;
  color: #94a3b8;
}

/* Profile Management Styles */
.profile-management {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-header-section {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  overflow: hidden;
}

.profile-cover-edit {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cover-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.edit-cover-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-cover-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}

.profile-info-edit {
  padding: 2rem;
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.profile-avatar-section {
  position: relative;
  margin-top: -60px;
}

.profile-avatar {
  position: relative;
  width: 120px;
  height: 120px;
  border: 4px solid rgba(30, 41, 59, 0.8);
  border-radius: 50%;
  overflow: hidden;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.edit-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36px;
  height: 36px;
  background: var(--accent-600);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-avatar-btn:hover {
  background: var(--accent-700);
}

.online-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background: var(--success-500);
  border: 3px solid rgba(30, 41, 59, 0.8);
  border-radius: 50%;
}

.profile-details-edit {
  flex: 1;
}

.profile-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.profile-edit-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  overflow: hidden;
}

.profile-edit-card.full-width {
  grid-column: 1 / -1;
}

/* Skills Edit */
.skills-edit-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skill-edit-item {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.skill-edit-item input[type="text"] {
  flex: 1;
}

.skill-range {
  width: 100px;
  accent-color: var(--accent-600);
}

.skill-percentage {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: var(--accent-400);
}

.btn-remove {
  background: var(--error-500);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-remove:hover {
  background: #dc2626;
}

/* Portfolio Edit */
.portfolio-edit-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.portfolio-edit-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
}

.portfolio-image-upload {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.portfolio-image-upload img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portfolio-image-upload:hover .upload-overlay {
  opacity: 1;
}

.upload-btn {
  padding: 0.75rem 1.5rem;
  background: var(--accent-600);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-btn:hover {
  background: var(--accent-700);
}

.portfolio-edit-info {
  padding: 1.5rem;
}

.portfolio-tags-edit {
  margin-bottom: 1rem;
}

.portfolio-actions {
  display: flex;
  gap: 0.5rem;
}

/* Social Links Edit */
.social-links-edit {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.social-links-edit .form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.social-links-edit .form-group label i {
  width: 20px;
  text-align: center;
  color: var(--accent-400);
}

/* Privacy Settings */
.privacy-settings {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.setting-info h4 {
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.setting-info p {
  color: #cbd5e1;
  font-size: 0.875rem;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent-600);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-toggle {
    display: block;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .services-grid,
  .freelancers-grid,
  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .user-menu .user-name {
    display: none;
  }

  .dashboard-tabs {
    overflow-x: auto;
  }

  .profile-info-edit {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-content-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}

/* Project Search Styles */
.project-search-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.search-header {
  text-align: center;
  margin-bottom: 2rem;
}

.search-header h3 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.search-header p {
  color: #cbd5e1;
  font-size: 1.125rem;
}

.project-search-filters {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
}

.search-filters-advanced {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #f8fafc;
}

.search-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

/* Project Results */
.project-search-results {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.results-header h4 {
  font-size: 1.25rem;
  margin: 0;
}

.sort-options select {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #f8fafc;
}

.project-results-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.project-result-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.project-result-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.project-header h4 {
  font-size: 1.25rem;
  margin: 0;
  color: #f8fafc;
}

.project-budget {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--accent-400);
}

.project-description {
  margin-bottom: 1.5rem;
}

.project-description p {
  color: #cbd5e1;
  line-height: 1.6;
}

.project-meta {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.meta-item i {
  color: var(--accent-400);
}

.project-skills {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.project-skills .skill-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-300);
  border-radius: 12px;
  font-size: 0.75rem;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.project-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Project Application Modal */
.project-application-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.project-info-display {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.project-info-display h4 {
  margin-bottom: 0.5rem;
  color: #f8fafc;
}

.project-info-display p {
  color: var(--accent-400);
  font-weight: 600;
  margin: 0;
}

.portfolio-samples {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 1rem;
}

.sample-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sample-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--accent-600);
}

.sample-item label {
  color: #f8fafc;
  cursor: pointer;
}

/* Responsive Design for Project Search */
@media (max-width: 768px) {
  .filter-row {
    grid-template-columns: 1fr;
  }

  .project-header {
    flex-direction: column;
    gap: 0.5rem;
  }

  .project-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .project-actions {
    justify-content: stretch;
  }

  .project-actions .btn {
    flex: 1;
  }

  .search-actions {
    flex-direction: column;
  }

  .results-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}

/* Search Sub-tabs */
.search-sub-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  padding: 0.5rem;
  background: rgba(30, 41, 59, 0.8);
  border-radius: 16px;
}

.sub-tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: #cbd5e1;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex: 1;
  justify-content: center;
}

.sub-tab-btn:hover,
.sub-tab-btn.active {
  background: var(--accent-600);
  color: white;
}

.search-sub-pane {
  display: none;
}

.search-sub-pane.active {
  display: block;
}

/* Client Search Styles */
.client-search-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.client-search-filters {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
}

.client-search-results {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
}

.client-results-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.client-result-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.client-result-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.client-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.client-info {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.client-logo {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  object-fit: cover;
  flex-shrink: 0;
}

.client-details h4 {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
  color: #f8fafc;
}

.client-details p {
  color: #cbd5e1;
  margin-bottom: 0.5rem;
}

.client-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.client-location i {
  color: var(--accent-400);
}

.client-status .status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.client-status .status-badge.active {
  background: rgba(34, 197, 94, 0.2);
  color: var(--success-500);
}

.client-description {
  margin-bottom: 1.5rem;
}

.client-description p {
  color: #cbd5e1;
  line-height: 1.6;
}

.client-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.client-stats .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.client-stats .stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--accent-400);
}

.client-stats .stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
}

.client-needs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.need-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(139, 92, 246, 0.2);
  color: #a78bfa;
  border-radius: 12px;
  font-size: 0.75rem;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.client-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Responsive Design for Client Search */
@media (max-width: 768px) {
  .sub-tab-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .client-header {
    flex-direction: column;
    gap: 1rem;
  }

  .client-info {
    flex-direction: column;
    text-align: center;
  }

  .client-stats {
    justify-content: center;
    gap: 1rem;
  }

  .client-actions {
    justify-content: stretch;
  }

  .client-actions .btn {
    flex: 1;
  }
}

/* User Type Button Styles */
.user-type-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.user-type-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(30, 41, 59, 0.8);
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  color: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.user-type-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: var(--accent-400);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.user-type-btn.active {
  background: var(--accent-600);
  border-color: var(--accent-500);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.user-type-btn.active:hover {
  background: var(--accent-700);
  border-color: var(--accent-600);
}

.user-type-btn i {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.user-type-btn span {
  font-size: 0.875rem;
  text-align: center;
}

/* Responsive Design for User Type Buttons */
@media (max-width: 768px) {
  .user-type-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .user-type-btn {
    flex-direction: row;
    justify-content: center;
    padding: 0.75rem;
  }

  .user-type-btn i {
    font-size: 1.25rem;
    margin-bottom: 0;
    margin-right: 0.5rem;
  }
}
