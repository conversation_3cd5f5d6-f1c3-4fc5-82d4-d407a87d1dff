// Admin Dashboard JavaScript

// Admin Data - Initial values, will be updated by fetched data
const adminData = {
    systemStats: {
        totalUsers: 0,           // Will be updated by fetchUsers
        pendingApprovals: 0,     // Will be updated by fetchUsers (pendingUsers)
        activeProjects: 0,       // Will be updated by fetchProjects
        systemIssues: 0          // Assuming this will also be fetched or remain 0 initially, updated by the fetch
    },
    pendingUsers: [], // This will now be populated by fetched data
    systemIssues: [], // Can be populated by an API call for system issues
    complaints: []    // Can be populated by an API call for complaints
};

// Function to fetch total users and pending users
async function fetchUsers() {
    try {
        const response = await fetch('http://localhost:5000/api/users');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const users = await response.json();

        const totalUsers = users.length;
        // Filter for users with status 'pending' (case-insensitive for robustness)
        const pendingUsers = users.filter(user => user.status && user.status.toLowerCase() === 'pending');

        adminData.systemStats.totalUsers = totalUsers;
        adminData.systemStats.pendingApprovals = pendingUsers.length; // Count of pending users
        adminData.pendingUsers = pendingUsers; // Store actual pending user objects

        console.log('Admin Dashboard - Fetched Total Users:', totalUsers);
        console.log('Admin Dashboard - Fetched Pending Users:', pendingUsers.length);

        // Update HTML elements for counts
        const totalUsersElement = document.getElementById('totalUsersCount');
        if (totalUsersElement) totalUsersElement.textContent = totalUsers;

        const pendingApprovalsElement = document.getElementById('pendingApprovalsCount');
        if (pendingApprovalsElement) pendingApprovalsElement.textContent = pendingUsers.length;

        // Render the detailed pending users list
        renderPendingUserList(adminData.pendingUsers);

    } catch (error) {
        console.error('Error fetching users for admin dashboard:', error);
        if (typeof showNotification !== 'undefined') {
            showNotification('Failed to load user data.', 'error');
        }
    }
}

// Function to fetch projects and active projects
async function fetchProjects() {
    try {
        const response = await fetch('http://localhost:5000/api/projects');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const projects = await response.json();

        // Assuming active projects are those not cancelled or completed
        const activeProjects = projects.filter(project => project.status !== 'cancelled' && project.status !== 'completed').length;

        adminData.systemStats.activeProjects = activeProjects;

        console.log('Admin Dashboard - Fetched Total Projects:', projects.length);
        console.log('Admin Dashboard - Fetched Active Projects:', activeProjects);

        // Update HTML elements
        const activeProjectsElement = document.getElementById('activeProjectsCount');
        if (activeProjectsElement) activeProjectsElement.textContent = activeProjects;

    } catch (error) {
        console.error('Error fetching projects for admin dashboard:', error);
        if (typeof showNotification !== 'undefined') {
            showNotification('Failed to load project data.', 'error');
        }
    }
}

// Function to fetch system health data
async function fetchSystemHealth() {
    try {
        const response = await fetch('http://localhost:5000/health');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const healthData = await response.json();

        console.log('Admin Dashboard - Fetched System Health:', healthData);

        // Update Health Indicator
        const healthIndicator = document.getElementById('systemHealthIndicator');
        const healthStatusText = document.getElementById('healthStatusText');
        if (healthIndicator && healthStatusText) {
            if (healthData.status === 'ok') {
                healthIndicator.classList.remove('unhealthy', 'warning');
                healthIndicator.classList.add('healthy');
                healthStatusText.textContent = 'Server is healthy 🚀';
            } else {
                healthIndicator.classList.remove('healthy');
                healthIndicator.classList.add('unhealthy');
                healthStatusText.textContent = 'Unhealthy';
            }
        }

        // Update Server Uptime
        const serverUptimeValue = document.getElementById('serverUptimeValue');
        if (serverUptimeValue && healthData.uptime !== undefined) {
            const uptimeSeconds = healthData.uptime;
            const days = Math.floor(uptimeSeconds / (3600 * 24));
            const hours = Math.floor((uptimeSeconds % (3600 * 24)) / 3600);
            const minutes = Math.floor((uptimeSeconds % 3600) / 60);
            const seconds = Math.floor(uptimeSeconds % 60);

            let uptimeString = '';
            if (days > 0) uptimeString += `${days}d `;
            if (hours > 0 || days > 0) uptimeString += `${hours}h `;
            if (minutes > 0 || hours > 0 || days > 0) uptimeString += `${minutes}m `;
            uptimeString += `${seconds}s`;

            serverUptimeValue.textContent = uptimeString.trim();
        }

    } catch (error) {
        console.error('Error fetching system health data for admin dashboard:', error);
        const healthIndicator = document.getElementById('systemHealthIndicator');
        const healthStatusText = document.getElementById('healthStatusText');
        if (healthIndicator && healthStatusText) {
            healthIndicator.classList.remove('healthy');
            healthIndicator.classList.add('unhealthy');
            healthStatusText.textContent = 'Error';
        }
        const serverUptimeValue = document.getElementById('serverUptimeValue');
        if (serverUptimeValue) serverUptimeValue.textContent = 'N/A';
        
        if (typeof showNotification !== 'undefined') {
            showNotification('Failed to load system health data.', 'error');
        }
    }
}


// Function to load all dashboard data (users, projects, etc.)
async function loadDashboardData() {
    console.log('Loading dashboard data...');
    await Promise.all([
        fetchUsers(),
        fetchProjects(),
        fetchSystemHealth()
    ]);
    console.log('Dashboard data loaded. Current adminData.systemStats:', adminData.systemStats);

    setupCharts();
}


document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin Dashboard DOM loaded.');

    initializeDashboard();
    setupEventListeners();

    loadDashboardData(); // This will now trigger fetching and rendering of pending users

    // Initial rendering of other lists (if they are not fetched by loadDashboardData)
    // You might want to add fetch functions for these as well.
    renderIssueList(adminData.systemIssues);
    renderComplaintList(adminData.complaints);

    // Fallback for showNotification if it's not defined by main.js
    if (typeof showNotification === 'undefined') {
        window.showNotification = function(message, type = 'info') {
            console.log(`Notification (${type}): ${message}`);
            const tempNotification = document.createElement('div');
            tempNotification.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background-color: ${type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue'};
                color: white;
                padding: 10px;
                border-radius: 5px;
                z-index: 1000;
            `;
            tempNotification.textContent = message;
            document.body.appendChild(tempNotification);
            setTimeout(() => tempNotification.remove(), 3000);
        };
    }
});

// --- Existing functions from your admin-dashboard.js below ---

function initializeDashboard() {
    // These will be overridden by fetchUsers/fetchProjects once they complete.
    document.getElementById('totalUsersCount').textContent = adminData.systemStats.totalUsers;
    document.getElementById('pendingApprovalsCount').textContent = adminData.systemStats.pendingApprovals;
    document.getElementById('activeProjectsCount').textContent = adminData.systemStats.activeProjects;
    document.getElementById('systemIssuesCount').textContent = adminData.systemStats.systemIssues;
}

function setupEventListeners() {
    document.querySelectorAll('.sidebar-nav a').forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                const targetSectionId = this.getAttribute('href').substring(1);
                showSection(targetSectionId);
            }
        });
    });

    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }

    const systemSettingsBtn = document.getElementById('systemSettingsBtn');
    if (systemSettingsBtn) {
        systemSettingsBtn.addEventListener('click', () => openModal('systemSettingsModal'));
    }

    const addAdminBtn = document.getElementById('addAdminBtn');
    if (addAdminBtn) {
        addAdminBtn.addEventListener('click', () => openModal('addAdminModal'));
    } else {
        console.warn("Element with ID 'addAdminBtn' not found. Its event listener will not be set.");
    }

    const systemSettingsForm = document.querySelector('.system-settings-form');
    if (systemSettingsForm) {
        systemSettingsForm.addEventListener('submit', handleSystemSettingsSubmit);
    }
}

function showSection(sectionId) {
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    document.getElementById(sectionId).classList.add('active');

    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`.tab-btn[data-tab="${sectionId}"]`).classList.add('active');
}


function handleLogout() {
    if (typeof showNotification !== 'undefined') {
        showNotification('Logging out...', 'info');
    }
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 500);
}

function handleSystemSettingsSubmit(event) {
    event.preventDefault();
    if (typeof showNotification !== 'undefined') {
        showNotification('Saving system settings...', 'info');
    }
    setTimeout(() => {
        if (typeof showNotification !== 'undefined') {
            showNotification('System settings saved!', 'success');
        }
        if (typeof closeModal !== 'undefined') {
            closeModal('systemSettingsModal');
        }
    }, 1000);
}

function setupCharts() {
    console.log('Setting up charts...');

    const userGrowthCtx = document.getElementById('userGrowthChart')?.getContext('2d');
    if (userGrowthCtx) {
        new Chart(userGrowthCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                datasets: [{
                    label: 'New Users',
                    data: [65, 59, 80, 81, 56, 55, 40],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    const projectStatusCtx = document.getElementById('projectStatusChart')?.getContext('2d');
    if (projectStatusCtx) {
        new Chart(projectStatusCtx, {
            type: 'pie',
            data: {
                labels: ['Active Projects', 'Completed Projects', 'Pending Projects'],
                datasets: [{
                    data: [adminData.systemStats.activeProjects, 200, 100],
                    backgroundColor: ['#28a745', '#007bff', '#ffc107']
                }]
            },
            options: {
                responsive: true
            }
        });
    }
}

// User Management Functions
// NEW: Function to render the pending user list
function renderPendingUserList(users) {
    const container = document.getElementById('pendingUserListContainer');
    if (!container) return;

    container.innerHTML = ''; // Clear existing content

    if (users.length === 0) {
        container.innerHTML = '<p class="no-data-message">No pending user approvals.</p>';
        return;
    }

    users.forEach(user => {
        const userItem = document.createElement('div');
        userItem.classList.add('pending-user-item');
        userItem.setAttribute('data-user-id', user._id); // Use _id from MongoDB

        // Determine user type specific meta info
        let userMetaContent = '';
        let userTypeDisplay = user.role || 'N/A'; // Assuming 'role' field
        let avatarSrc = user.addPhoto || `https://placehold.co/50x50/aabbcc/ffffff?text=${user.name ? user.name.charAt(0) : 'U'}`; // Placeholder avatar

        if (user.role === 'freelancer' && user.skills) {
            userMetaContent = `<span class="registration-date">Registered: ${user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</span>
                                <span class="user-skills">${user.skills.map(s => s.name).join(', ')}</span>`; // Assuming skills is an array of objects with 'name'
        } else if (user.role === 'client' && user.companyName) {
            userMetaContent = `<span class="registration-date">Registered: ${user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</span>
                                <span class="user-company">${user.companyName}</span>`;
        } else {
             userMetaContent = `<span class="registration-date">Registered: ${user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</span>`;
        }


        userItem.innerHTML = `
            <div class="user-info">
                <img src="${avatarSrc}" alt="User Avatar" class="user-avatar">
                <div class="user-details">
                    <h4>${user.firstName} ${ user.lastName || 'User'}</h4>
                    <p>${user.email || 'N/A'}</p>
                    <div class="user-type">${userTypeDisplay}</div>
                </div>
            </div>
            <div class="user-meta">
                ${userMetaContent}
            </div>
            <div class="user-actions">
                <button class="btn btn-success btn-sm" onclick="approveUser('${user._id}')">
                    <i class="fas fa-check"></i>
                    Approve
                </button>
                <button class="btn btn-danger btn-sm" onclick="rejectUser('${user._id}')">
                    <i class="fas fa-times"></i>
                    Reject
                </button>
                <button class="btn btn-outline btn-sm" onclick="viewUserDetails('${user._id}')">
                    <i class="fas fa-eye"></i>
                    View
                </button>
            </div>
        `;
        container.appendChild(userItem);
    });
}


// This function is for the "All Users" table, not the pending list
function renderUserList(users) {
    const tbody = document.getElementById('allUsersTableBody');
    if (!tbody) return;

    tbody.innerHTML = ''; // Clear existing content

    if (users.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">No users found.</td></tr>';
        return;
    }

    users.forEach(user => {
        const row = document.createElement('tr');
        const userTypeBadgeClass = user.role ? user.role.toLowerCase() : 'default';
        const statusBadgeClass = user.status ? user.status.toLowerCase() : 'default';
        const joinedDate = user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A';
        const ratingValue = user.rating || 0; // Assuming a rating field
        const avatarInitials = user.fullName ? user.fullName.split(' ').map(n => n.charAt(0)).join('') : 'U';
        const avatarSrc = user.addPhoto || `https://placehold.co/40x40/aabbcc/ffffff?text=${avatarInitials}`;


        row.innerHTML = `
            <td>
                <div class="user-cell">
                    <img src="${avatarSrc}" alt="User Avatar" class="table-avatar">
                    <div class="user-info">
                        <div class="user-name">${user.firstName || user.lastName || 'Unknown User'}</div>
                        <div class="user-email">${user.email || 'N/A'}</div>
                    </div>
                </div>
            </td>
            <td><span class="user-type-badge ${userTypeBadgeClass}">${user.role || 'N/A'}</span></td>
            <td><span class="status-badge ${statusBadgeClass}">${user.status || 'N/A'}</span></td>
            <td>${joinedDate}</td>
            <td>
                <div class="rating-display">
                    <span class="rating-value">${ratingValue.toFixed(1)}</span>
                    <div class="rating-stars">
                        ${'<i class="fas fa-star"></i>'.repeat(Math.floor(ratingValue))}
                        ${ratingValue % 1 !== 0 ? '<i class="fas fa-star-half-alt"></i>' : ''}
                        ${'<i class="far fa-star"></i>'.repeat(5 - Math.ceil(ratingValue))}
                    </div>
                </div>
            </td>
            <td>
                <div class="table-actions">
                    <button class="btn btn-outline btn-xs" onclick="viewUser('${user._id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-warning btn-xs" onclick="suspendUser('${user._id}')">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="btn btn-danger btn-xs" onclick="deleteUser('${user._id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}


async function approveUser(userId) {
    console.log('Approving user:', userId);
    if (typeof showNotification === 'undefined') {
        console.error("showNotification function is not defined.");
        return;
    }

    showNotification('Approving user...', 'info');

    try {
        const response = await fetch(`http://localhost:5000/api/users/${userId}`, {
            method: 'PATCH', // Or PUT, depending on your API design for status updates
            headers: {
                'Content-Type': 'application/json',
                // 'Authorization': 'Bearer YOUR_ADMIN_TOKEN' // Add auth token if your API requires it
            },
            body: JSON.stringify({ status: 'approved' }) // Send the new status
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        showNotification(result.message || `User ${userId} approved successfully!`, 'success');
        refreshPendingUsers(); // Re-fetch and re-render the list

    } catch (error) {
        console.error('Error approving user:', error);
        showNotification(`Failed to approve user ${userId}: ${error.message}`, 'error');
    }
}

async function rejectUser(userId) {
    console.log('Rejecting user:', userId);
    if (typeof showNotification === 'undefined') {
        console.error("showNotification function is not defined.");
        return;
    }

    showNotification('Rejecting user...', 'info');

    try {
        const response = await fetch(`http://localhost:5000/api/users/${userId}`, {
            method: 'PATCH', // Or PUT
            headers: {
                'Content-Type': 'application/json',
                // 'Authorization': 'Bearer YOUR_ADMIN_TOKEN' // Add auth token if your API requires it
            },
            body: JSON.stringify({ status: 'rejected' }) // Send the new status
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        showNotification(result.message || `User ${userId} rejected successfully!`, 'warning');
        refreshPendingUsers(); // Re-fetch and re-render the list

    } catch (error) {
        console.error('Error rejecting user:', error);
        showNotification(`Failed to reject user ${userId}: ${error.message}`, 'error');
    }
}

function viewUserDetails(userId) {
    console.log('Viewing details for user:', userId);
    if (typeof showNotification !== 'undefined') {
        showNotification(`Opening details for ${userId}...`, 'info');
    }
    // Implement modal or new page for user details
}

async function approveAllUsers() {
    if (typeof showNotification === 'undefined') {
        console.error("showNotification function is not defined.");
        return;
    }

    showNotification('Approving all pending users...', 'info');

    // In a real application, this would typically involve a dedicated API endpoint
    // to approve all pending users, or iterate through adminData.pendingUsers
    // and send individual approve requests. For now, we'll simulate a batch action.
    try {
        // Example of a hypothetical batch approve API call:
        // const response = await fetch('http://localhost:5000/api/users/approve-all-pending', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     // body: JSON.stringify({ /* any necessary data */ })
        // });
        // if (!response.ok) {
        //     const errorData = await response.json();
        //     throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        // }
        // const result = await response.json();
        // showNotification(result.message || 'All pending users approved successfully!', 'success');

        // Simulating success after a delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        showNotification('All pending users approved (simulated)!', 'success');

        refreshPendingUsers(); // Re-fetch and re-render the list
    } catch (error) {
        console.error('Error approving all users:', error);
        showNotification(`Failed to approve all users: ${error.message}`, 'error');
    }
}

function refreshPendingUsers() {
    console.log('Refreshing pending users list...');
    fetchUsers(); // Re-fetch all users, which will update the pending list
}

function suspendUser(userId) {
    console.log('Suspending user:', userId);
    if (typeof showNotification !== 'undefined') {
        showNotification(`User ${userId} suspended (simulated).`, 'warning');
    }
    // API call to suspend user
}

function deleteUser(userId) {
    console.log('Deleting user:', userId);
    if (typeof showNotification !== 'undefined') {
        showNotification(`User ${userId} deleted (simulated).`, 'error');
    }
    // API call to delete user
}

function viewUser(userId) {
    console.log('Viewing user:', userId);
    if (typeof showNotification !== 'undefined') {
        showNotification(`Fetching details for user ${userId}...`, 'info');
    }
    // API call to get specific user details
}

// System Health Functions
function renderIssueList(issues) {
    const issueListContainer = document.getElementById('issueList');
    if (!issueListContainer) return;

    issueListContainer.innerHTML = ''; // Clear previous list

    if (issues.length === 0) {
        issueListContainer.innerHTML = '<p class="no-data-message">No system issues found.</p>';
        return;
    }

    issues.forEach(issue => {
        const issueRow = document.createElement('div');
        issueRow.className = 'list-item';
        issueRow.innerHTML = `
            <span>${issue.title}</span>
            <span>${issue.priority}</span>
            <span>${issue.reportedTime}</span>
            <span>${issue.affectedUsers}</span>
            <div class="list-actions">
                <button class="btn btn-sm btn-outline" onclick="viewIssueDetails('${issue.id}')">View</button>
                <button class="btn btn-sm btn-success" onclick="resolveIssue('${issue.id}')">Resolve</button>
            </div>
        `;
        issueListContainer.appendChild(issueRow);
    });
}

function generateReport() {
    if (typeof showNotification !== 'undefined') {
        showNotification('Generating system report...', 'info');
    }
    setTimeout(() => {
        if (typeof showNotification !== 'undefined') {
            showNotification('System report generated successfully (simulated)!', 'success');
        }
    }, 3000);
}

function runSystemDiagnostics() {
    if (typeof showNotification !== 'undefined') {
        showNotification('Running quick system diagnostics...', 'info');
    }
    setTimeout(() => {
        if (typeof showNotification !== 'undefined') {
            showNotification('Quick diagnostics completed. No critical issues found (simulated).', 'success');
        }
    }, 2000);
}

function runFullDiagnostics() {
    if (typeof showNotification !== 'undefined') {
        showNotification('Running full system diagnostics (this may take a while)...', 'info');
    }
    setTimeout(() => {
        if (typeof showNotification !== 'undefined') {
            showNotification('Full diagnostics completed. Review logs for details (simulated).', 'warning');
        }
    }, 5000);
}

function resolveIssue(issueId) {
    console.log('Resolving issue:', issueId);
    if (typeof showNotification !== 'undefined') {
        showNotification(`Issue ${issueId} marked as resolved (simulated).`, 'success');
    }
}

function viewIssueDetails(issueId) {
    console.log('Viewing issue details:', issueId);
    if (typeof showNotification !== 'undefined') {
        showNotification(`Opening ${issueId} details...`, 'info');
    }
}

// Complaint Management Functions
function renderComplaintList(complaints) {
    const complaintListContainer = document.getElementById('complaintList');
    if (!complaintListContainer) return;

    complaintListContainer.innerHTML = ''; // Clear previous list

    if (complaints.length === 0) {
        complaintListContainer.innerHTML = '<p class="no-data-message">No user complaints found.</p>';
        return;
    }

    complaints.forEach(complaint => {
        const complaintRow = document.createElement('div');
        complaintRow.className = 'list-item';
        complaintRow.innerHTML = `
            <span>${complaint.user}</span>
            <span>${complaint.subject}</span>
            <span>${complaint.date}</span>
            <span>${complaint.status}</span>
            <div class="list-actions">
                <button class="btn btn-sm btn-outline" onclick="viewComplaintDetails('${complaint.id}')">View</button>
                <button class="btn btn-sm btn-info" onclick="respondToComplaint('${complaint.id}')">Respond</button>
                <button class="btn btn-sm btn-success" onclick="resolveComplaint('${complaint.id}')">Resolve</button>
                <button class="btn btn-sm btn-danger" onclick="escalateComplaint('${complaint.id}')">Escalate</button>
            </div>
        `;
        complaintListContainer.appendChild(complaintRow);
    });
}

function respondToComplaint(complaintId) {
    console.log('Responding to complaint:', complaintId);
    if (typeof showNotification !== 'undefined') {
        showNotification('Opening response interface...', 'info');
    }
}

function resolveComplaint(complaintId) {
    console.log('Resolving complaint:', complaintId);
    if (typeof showNotification !== 'undefined') {
        showNotification(`Complaint ${complaintId} marked as resolved (simulated).`, 'success');
    }
}

function escalateComplaint(complaintId) {
    console.log('Escalating complaint:', complaintId);
    if (typeof showNotification !== 'undefined') {
        showNotification('Complaint escalated to senior support (simulated).', 'warning');
    }
}

function viewComplaintDetails(complaintId) {
    console.log('Viewing details for complaint:', complaintId);
    if (typeof showNotification !== 'undefined') {
        showNotification(`Opening details for complaint ${complaintId}...`, 'info');
    }
}

// Content Management Functions
function previewChanges() {
    if (typeof showNotification !== 'undefined') {
        showNotification('Opening preview mode...', 'info');
    }
}

function saveContentChanges() {
    if (typeof showNotification !== 'undefined') {
        showNotification('Saving content changes...', 'info');
    }
    setTimeout(() => {
        if (typeof showNotification !== 'undefined') {
            showNotification('Content changes saved successfully (simulated)!', 'success');
        }
    }, 1500);
}

function editSection(sectionName) {
    console.log('Editing section:', sectionName);
    if (typeof showNotification !== 'undefined') {
        showNotification(`Opening ${sectionName} editor...`, 'info');
    }
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Notification function (if not already defined)
if (typeof showNotification === 'undefined') {
    window.showNotification = function(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        const notificationDiv = document.createElement('div');
        notificationDiv.textContent = message;
        notificationDiv.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: ${type === 'error' ? 'red' : type === 'success' ? 'green' : '#333'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 1000;
        `;
        document.body.appendChild(notificationDiv);
        setTimeout(() => notificationDiv.remove(), 3000);
    };
}

// Export functions for global use
window.approveUser = approveUser;
window.rejectUser = rejectUser;
window.viewUserDetails = viewUserDetails;
window.approveAllUsers = approveAllUsers;
window.refreshPendingUsers = refreshPendingUsers;
window.suspendUser = suspendUser;
window.deleteUser = deleteUser;
window.viewUser = viewUser;
window.generateReport = generateReport;
window.runSystemDiagnostics = runSystemDiagnostics;
window.runFullDiagnostics = runFullDiagnostics;
window.resolveIssue = resolveIssue;
window.viewIssueDetails = viewIssueDetails;
window.respondToComplaint = respondToComplaint;
window.resolveComplaint = resolveComplaint;
window.escalateComplaint = escalateComplaint;
window.previewChanges = previewChanges;
window.saveContentChanges = saveContentChanges;
window.editSection = editSection;

console.log('Admin Dashboard JavaScript loaded successfully!');