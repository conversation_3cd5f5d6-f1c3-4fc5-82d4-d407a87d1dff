<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - PortfolioPro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <h2 class="text-gradient">PortfolioPro Admin</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="admin-dashboard.html" class="nav-link active">Admin Panel</a></li>
                    <li><a href="#" class="nav-link">System Logs</a></li>
                    <li><a href="#" class="nav-link">Reports</a></li>
                </ul>
            </div>
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="Admin" class="user-avatar">
                    <span class="user-name">Admin</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Dashboard Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="welcome-section">
                    <h1>Admin Dashboard 🛠️</h1>
                    <p>System control and management center</p>
                </div>
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="openModal('systemSettingsModal')">
                        <i class="fas fa-cog"></i>
                        System Settings
                    </button>
                    <button class="btn btn-outline" onclick="generateReport()">
                        <i class="fas fa-chart-bar"></i>
                        Generate Report
                    </button>
                </div>
            </div>

            <!-- System Stats -->

            <div class="stats-grid">
                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>  
                        <div class="stat-content">
                        <h3><span class="stat-value" id="totalUsersCount">0</span></h3>
                        <p>Total Users</p>
                    </div>
                </div>
                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-clock icon-orange"></i>
                    </div>  
                        <div class="stat-content">
                        <h3><span class="stat-value" id="pendingApprovalsCount">0</span></h3>
                        <p>Pending Approvals</p>
                    </div>
                </div>
                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-project-diagram icon-green"></i>
                    </div>  
                        <div class="stat-content">
                        <h3><span class="stat-value" id="activeProjectsCount">0</span></h3>
                        <p>Active Projects</p>
                    </div>
                </div>
                <div class="stat-card glass-card">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle icon-red"></i>
                    </div>  
                        <div class="stat-content">
                        <h3><span class="stat-value" id="systemIssuesCount">0</span></h3>
                        <p>System Issues</p>
                    </div>
                </div>

            </div>

            <!-- Admin Tabs -->
            <div class="dashboard-tabs">
                <button class="tab-btn active" onclick="switchTab('overview')">
                    <i class="fas fa-chart-line"></i>
                    Overview
                </button>
                <button class="tab-btn" onclick="switchTab('users')">
                    <i class="fas fa-users"></i>
                    User Management
                </button>
                <button class="tab-btn" onclick="switchTab('analytics')">
                    <i class="fas fa-chart-bar"></i>
                    Analytics
                </button>
                <button class="tab-btn" onclick="switchTab('troubleshooting')">
                    <i class="fas fa-tools"></i>
                    Troubleshooting
                </button>
                <button class="tab-btn" onclick="switchTab('ui-management')">
                    <i class="fas fa-palette"></i>
                    UI/UX Management
                </button>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Overview Tab -->
                <div id="overview" class="tab-pane active">
                    <div class="dashboard-grid">
                        <!-- System Health -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>System Health</h3>
                                <div class="health-indicator healthy" id="systemHealthIndicator">
                                    <i class="fas fa-circle"></i>
                                    <span id="healthStatusText">Healthy</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="health-metrics">
                                    <div class="metric-item">
                                        <div class="metric-label">Server Uptime</div>
                                        <div class="metric-value" id="serverUptimeValue">Loading...</div>
                                        <div class="metric-bar">
                                            <div class="metric-fill" id="serverUptimeFill" style="width: 100%"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-item">
                                        <div class="metric-label">API Response Time</div>
                                        <div class="metric-value" id="apiResponseTimeValue">120ms</div>
                                        <div class="metric-status good" id="apiResponseTimeStatus">Good</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Recent Activity -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Recent System Activity</h3>
                                <a href="#" class="view-all">View All Logs</a>
                            </div>
                            <div class="card-body">
                                <div class="activity-list">
                                    <div class="activity-item">
                                        <div class="activity-icon user-action">
                                            <i class="fas fa-user-plus"></i>
                                        </div>
                                        <div class="activity-info">
                                            <p>New user registration: <EMAIL></p>
                                            <span class="activity-time">2 minutes ago</span>
                                        </div>
                                    </div>
                                    
                                    <div class="activity-item">
                                        <div class="activity-icon system-action">
                                            <i class="fas fa-cog"></i>
                                        </div>
                                        <div class="activity-info">
                                            <p>System backup completed successfully</p>
                                            <span class="activity-time">15 minutes ago</span>
                                        </div>
                                    </div>
                                    
                                    <div class="activity-item">
                                        <div class="activity-icon warning-action">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="activity-info">
                                            <p>High CPU usage detected on server-02</p>
                                            <span class="activity-time">1 hour ago</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Quick Actions</h3>
                            </div>
                            <div class="card-body">
                                <div class="quick-actions-grid">
                                    <button class="quick-action-btn" onclick="approveUsers()">
                                        <i class="fas fa-user-check"></i>
                                        <span>Approve Users</span>
                                        <div class="action-badge">23</div>
                                    </button>
                                    
                                    <button class="quick-action-btn" onclick="reviewReports()">
                                        <i class="fas fa-flag"></i>
                                        <span>Review Reports</span>
                                        <div class="action-badge">7</div>
                                    </button>
                                    
                                    <button class="quick-action-btn" onclick="systemMaintenance()">
                                        <i class="fas fa-tools"></i>
                                        <span>Maintenance</span>
                                    </button>
                                    
                                    <button class="quick-action-btn" onclick="backupSystem()">
                                        <i class="fas fa-database"></i>
                                        <span>Backup System</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Management Tab -->
                <div id="users" class="tab-pane">
                    <div class="user-management-section">
                        <!-- Pending Users -->
                        <div class="pending-users-card">
                            <div class="card-header">
                                <h3>Pending User Approvals</h3>
                                <div class="approval-actions">
                                    <button class="btn btn-success btn-sm" onclick="approveAllUsers()">
                                        <i class="fas fa-check-double"></i>
                                        Approve All
                                    </button>
                                    <button class="btn btn-outline btn-sm" onclick="refreshPendingUsers()">
                                        <i class="fas fa-refresh"></i>
                                        Refresh
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="pending-users-list" id="pendingUserListContainer">
                                    <!-- Dynamic pending user items will be loaded here by JavaScript -->
                                    <p class="no-data-message" id="noPendingUsersMessage">Loading pending users...</p>
                                </div>
                            </div>
                        </div>

                        <!-- User Search and Management -->
                        <div class="user-search-card">
                            <div class="card-header">
                                <h3>User Management</h3>
                                <div class="user-filters">
                                    <select class="filter-select">
                                        <option value="">All Users</option>
                                        <option value="freelancer">Freelancers</option>
                                        <option value="client">Clients</option>
                                        <option value="active">Active</option>
                                        <option value="suspended">Suspended</option>
                                    </select>
                                    <input type="text" class="search-input" placeholder="Search users...">
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="users-table">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Type</th>
                                                <th>Status</th>
                                                <th>Joined</th>
                                                <th>Rating</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="allUsersTableBody">
                                            <!-- Dynamic all user rows will be loaded here by JavaScript -->
                                            <tr><td colspan="6" class="text-center">Loading all users...</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="analytics" class="tab-pane">
                    <div class="analytics-section">
                        <!-- Platform Analytics -->
                        <div class="analytics-overview">
                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Platform Analytics</h3>
                                    <div class="date-range-selector">
                                        <select class="form-input">
                                            <option>Last 7 days</option>
                                            <option>Last 30 days</option>
                                            <option>Last 90 days</option>
                                            <option>Last year</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="analytics-stats-grid">
                                        <div class="analytics-stat">
                                            <div class="stat-icon">
                                                <i class="fas fa-users"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h4>1,234</h4>
                                                <p>Total Users</p>
                                                <span class="stat-change positive">+12.5%</span>
                                            </div>
                                        </div>
                                        
                                        <div class="analytics-stat">
                                            <div class="stat-icon">
                                                <i class="fas fa-project-diagram"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h4>456</h4>
                                                <p>Active Projects</p>
                                                <span class="stat-change positive">+8.3%</span>
                                            </div>
                                        </div>
                                        
                                        <div class="analytics-stat">
                                            <div class="stat-icon">
                                                <i class="fas fa-dollar-sign"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h4>$45,230</h4>
                                                <p>Platform Revenue</p>
                                                <span class="stat-change positive">+15.2%</span>
                                            </div>
                                        </div>
                                        
                                        <div class="analytics-stat">
                                            <div class="stat-icon">
                                                <i class="fas fa-chart-line"></i>
                                            </div>
                                            <div class="stat-info">
                                                <h4>89%</h4>
                                                <p>Success Rate</p>
                                                <span class="stat-change positive">+2.1%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User Activity Chart -->
                        <div class="user-activity-chart">
                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>User Activity Trends</h3>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <div class="chart-placeholder">
                                            <div class="chart-bars">
                                                <div class="chart-bar" style="height: 60%"></div>
                                                <div class="chart-bar" style="height: 80%"></div>
                                                <div class="chart-bar" style="height: 45%"></div>
                                                <div class="chart-bar" style="height: 90%"></div>
                                                <div class="chart-bar" style="height: 70%"></div>
                                                <div class="chart-bar" style="height: 85%"></div>
                                                <div class="chart-bar" style="height: 95%"></div>
                                            </div>
                                            <div class="chart-labels">
                                                <span>Mon</span>
                                                <span>Tue</span>
                                                <span>Wed</span>
                                                <span>Thu</span>
                                                <span>Fri</span>
                                                <span>Sat</span>
                                                <span>Sun</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Top Performers -->
                        <div class="top-performers">
                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>Top Performing Freelancers</h3>
                                </div>
                                <div class="card-body">
                                    <div class="performers-list">
                                        <div class="performer-item">
                                            <div class="performer-rank">1</div>
                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face" alt="Freelancer" class="performer-avatar">
                                            <div class="performer-info">
                                                <h4>Eleni Berhan</h4>
                                                <p>Full Stack Developer</p>
                                            </div>
                                            <div class="performer-stats">
                                                <div class="stat-item">
                                                    <span class="stat-value">4.9</span>
                                                    <span class="stat-label">Rating</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-value">127</span>
                                                    <span class="stat-label">Reviews</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-value">$12,450</span>
                                                    <span class="stat-label">Earnings</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="performer-item">
                                            <div class="performer-rank">2</div>
                                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face" alt="Freelancer" class="performer-avatar">
                                            <div class="performer-info">
                                                <h4>Hawinet Mkeonen</h4>
                                                <p>UI/UX Designer</p>
                                            </div>
                                            <div class="performer-stats">
                                                <div class="stat-item">
                                                    <span class="stat-value">4.8</span>
                                                    <span class="stat-label">Rating</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-value">89</span>
                                                    <span class="stat-label">Reviews</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-value">$9,230</span>
                                                    <span class="stat-label">Earnings</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting Tab -->
                <div id="troubleshooting" class="tab-pane">
                    <div class="troubleshooting-section">
                        <!-- System Issues -->
                        <div class="system-issues-card">
                            <div class="card-header">
                                <h3>Active System Issues</h3>
                                <div class="issue-filters">
                                    <select class="filter-select">
                                        <option value="">All Issues</option>
                                        <option value="critical">Critical</option>
                                        <option value="high">High Priority</option>
                                        <option value="medium">Medium Priority</option>
                                        <option value="low">Low Priority</option>
                                    </select>
                                    <button class="btn btn-primary btn-sm" onclick="runSystemDiagnostics()">
                                        <i class="fas fa-stethoscope"></i>
                                        Run Diagnostics
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="issues-list">
                                    <div class="issue-item critical">
                                        <div class="issue-icon">
                                            <i class="fas fa-exclamation-circle"></i>
                                        </div>
                                        <div class="issue-info">
                                            <h4>Database Connection Timeout</h4>
                                            <p>Multiple users reporting slow loading times and connection errors</p>
                                            <div class="issue-meta">
                                                <span class="issue-priority critical">Critical</span>
                                                <span class="issue-time">Reported: 2 hours ago</span>
                                                <span class="issue-affected">Affected: 45 users</span>
                                            </div>
                                        </div>
                                        <div class="issue-actions">
                                            <button class="btn btn-primary btn-sm" onclick="resolveIssue('db-timeout')">
                                                <i class="fas fa-wrench"></i>
                                                Fix Now
                                            </button>
                                            <button class="btn btn-outline btn-sm" onclick="viewIssueDetails('db-timeout')">
                                                <i class="fas fa-eye"></i>
                                                Details
                                            </button>
                                        </div>
                                    </div>

                                    <div class="issue-item high">
                                        <div class="issue-icon">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="issue-info">
                                            <h4>Payment Gateway Error</h4>
                                            <p>Some transactions failing during checkout process</p>
                                            <div class="issue-meta">
                                                <span class="issue-priority high">High</span>
                                                <span class="issue-time">Reported: 4 hours ago</span>
                                                <span class="issue-affected">Affected: 12 users</span>
                                            </div>
                                        </div>
                                        <div class="issue-actions">
                                            <button class="btn btn-primary btn-sm" onclick="resolveIssue('payment-error')">
                                                <i class="fas fa-wrench"></i>
                                                Fix Now
                                            </button>
                                            <button class="btn btn-outline btn-sm" onclick="viewIssueDetails('payment-error')">
                                                <i class="fas fa-eye"></i>
                                                Details
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Diagnostics -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>System Diagnostics</h3>
                            </div>
                            <div class="card-body">
                                <div class="diagnostics-actions">
                                    <button class="btn btn-primary" onclick="runSystemDiagnostics()">Run Quick Diagnostics</button>
                                    <button class="btn btn-outline" onclick="runFullDiagnostics()">Run Full Diagnostics</button>
                                </div>
                            </div>
                        </div>

                        <!-- User Complaints -->
                        <div class="user-complaints-card">
                            <div class="card-header">
                                <h3>User Complaints & Reports</h3>
                                <div class="complaint-filters">
                                    <select class="filter-select">
                                        <option value="">All Complaints</option>
                                        <option value="technical">Technical Issues</option>
                                        <option value="billing">Billing Problems</option>
                                        <option value="user-behavior">User Behavior</option>
                                        <option value="feature-request">Feature Requests</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="complaints-list">
                                    <div class="complaint-item">
                                        <div class="complaint-header">
                                            <div class="user-info">
                                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="User" class="complaint-avatar">
                                                <div class="user-details">
                                                    <h4>Alex Rodriguez</h4>
                                                    <p><EMAIL></p>
                                                </div>
                                            </div>
                                            <div class="complaint-meta">
                                                <span class="complaint-type technical">Technical</span>
                                                <span class="complaint-time">2 hours ago</span>
                                            </div>
                                        </div>
                                        <div class="complaint-content">
                                            <h5>Portfolio upload failing repeatedly</h5>
                                            <p>I've been trying to upload my portfolio images for the past hour, but the upload keeps failing at 80%. This is very frustrating as I have a client meeting tomorrow.</p>
                                        </div>
                                        <div class="complaint-actions">
                                            <button class="btn btn-primary btn-sm" onclick="respondToComplaint('alex-complaint')">
                                                <i class="fas fa-reply"></i>
                                                Respond
                                            </button>
                                            <button class="btn btn-success btn-sm" onclick="resolveComplaint('alex-complaint')">
                                                <i class="fas fa-check"></i>
                                                Resolve
                                            </button>
                                            <button class="btn btn-outline btn-sm" onclick="escalateComplaint('alex-complaint')">
                                                <i class="fas fa-arrow-up"></i>
                                                Escalate
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- UI/UX Management Tab -->
                <div id="ui-management" class="tab-pane">
                    <div class="ui-management-section">
                        <!-- Theme Management -->
                        <div class="theme-management-card">
                            <div class="card-header">
                                <h3>Theme & Visual Settings</h3>
                                <button class="btn btn-primary btn-sm" onclick="previewChanges()">
                                    <i class="fas fa-eye"></i>
                                    Preview Changes
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="theme-settings">
                                    <div class="setting-group">
                                        <h4>Color Scheme</h4>
                                        <div class="color-options">
                                            <div class="color-option active" data-theme="default">
                                                <div class="color-preview">
                                                    <div class="color-primary" style="background: #3b82f6;"></div>
                                                    <div class="color-secondary" style="background: #1e293b;"></div>
                                                    <div class="color-accent" style="background: #60a5fa;"></div>
                                                </div>
                                                <span>Default Blue</span>
                                            </div>

                                            <div class="color-option" data-theme="green">
                                                <div class="color-preview">
                                                    <div class="color-primary" style="background: #10b981;"></div>
                                                    <div class="color-secondary" style="background: #1e293b;"></div>
                                                    <div class="color-accent" style="background: #34d399;"></div>
                                                </div>
                                                <span>Green</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="setting-group">
                                        <h4>Feature Management</h4>
                                        <div class="feature-list">
                                            <div class="feature-item">
                                                <div class="feature-info">
                                                    <h5>User Registration</h5>
                                                    <p>Allow new users to register on the platform</p>
                                                </div>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" checked>
                                                    <span class="toggle-slider"></span>
                                                </label>
                                            </div>

                                            <div class="feature-item">
                                                <div class="feature-info">
                                                    <h5>Project Posting</h5>
                                                    <p>Allow clients to post new projects</p>
                                                </div>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" checked>
                                                    <span class="toggle-slider"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modals -->
    <!-- System Settings Modal -->
    <div id="systemSettingsModal" class="modal">
        <div class="modal-content large">
            <span class="close" onclick="closeModal('systemSettingsModal')">&times;</span>
            <h2>System Settings</h2>
            <form class="system-settings-form">
                <div class="form-group">
                    <label>Platform Name</label>
                    <input type="text" class="form-input" value="PortfolioPro">
                </div>
                <div class="form-group">
                    <label>Maintenance Mode</label>
                    <label class="toggle-switch">
                        <input type="checkbox">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-outline" onclick="closeModal('systemSettingsModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Settings</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Back to Home -->
    <div class="floating-home-btn">
        <a href="index.html" class="btn btn-primary">
            <i class="fas fa-home"></i>
            Home
        </a>
    </div>

    <script src="scripts/auth-check.js"></script>
    <script src="scripts/main.js"></script>
    <script src="scripts/admin-dashboard.js"></script>
</body>
</html>