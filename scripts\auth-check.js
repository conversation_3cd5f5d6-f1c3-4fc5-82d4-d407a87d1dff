// Authentication Check Script
// Include this script in dashboard pages to ensure they're protected

(function() {
    'use strict';
    
    // Check if user is authenticated
    function checkAuthentication() {
        const token = localStorage.getItem('token');
        const user = localStorage.getItem('user');
        
        if (!token || !user) {
            console.log('User not authenticated, redirecting to login...');
            // Redirect to main page
            window.location.href = 'index.html';
            return false;
        }
        
        try {
            const userData = JSON.parse(user);
            console.log('User authenticated:', userData.fullName || userData.name);
            return userData;
        } catch (error) {
            console.error('Error parsing user data:', error);
            // Clear corrupted data and redirect
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = 'index.html';
            return false;
        }
    }
    
    // Check if user has the right role for this dashboard
    function checkDashboardAccess() {
        const userData = checkAuthentication();
        if (!userData) return false;
        
        const currentPage = window.location.pathname.split('/').pop();
        const userRole = userData.role;
        
        let expectedRole = null;
        if (currentPage.includes('freelancer')) expectedRole = 'freelancer';
        else if (currentPage.includes('client')) expectedRole = 'client';
        else if (currentPage.includes('admin')) expectedRole = 'admin';
        
        if (expectedRole && userRole !== expectedRole) {
            console.log(`Access denied. User role: ${userRole}, Required: ${expectedRole}`);
            // Redirect to appropriate dashboard
            redirectToCorrectDashboard(userRole);
            return false;
        }
        
        return userData;
    }
    
    function redirectToCorrectDashboard(userRole) {
        switch (userRole) {
            case 'freelancer':
                window.location.href = 'freelancer-dashboard.html';
                break;
            case 'client':
                window.location.href = 'client-dashboard.html';
                break;
            case 'admin':
                window.location.href = 'admin-dashboard.html';
                break;
            default:
                window.location.href = 'index.html';
        }
    }
    
    // Initialize authentication check when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        const userData = checkDashboardAccess();
        
        if (userData) {
            // Update any user-specific UI elements
            updateDashboardUI(userData);
        }
    });
    
    function updateDashboardUI(userData) {
        // Update user name displays
        const userNameElements = document.querySelectorAll('.user-name, .username, [data-user-name]');
        userNameElements.forEach(element => {
            element.textContent = userData.fullName || userData.name || 'User';
        });
        
        // Update user email displays
        const userEmailElements = document.querySelectorAll('.user-email, [data-user-email]');
        userEmailElements.forEach(element => {
            element.textContent = userData.email || '';
        });
        
        // Update user role displays
        const userRoleElements = document.querySelectorAll('.user-role, [data-user-role]');
        userRoleElements.forEach(element => {
            element.textContent = userData.role || '';
        });
        
        console.log('Dashboard UI updated for user:', userData.fullName || userData.name);
    }
    
    // Export functions for use in dashboard scripts
    window.getCurrentUser = function() {
        const user = localStorage.getItem('user');
        return user ? JSON.parse(user) : null;
    };
    
    window.logout = function() {
        console.log('Logging out user...');
        
        // Clear stored user data
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        
        // Show notification if available
        if (typeof showNotification === 'function') {
            showNotification('Logged out successfully!', 'success');
        }
        
        // Redirect to landing page
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1000);
    };
    
    window.isUserLoggedIn = function() {
        const token = localStorage.getItem('token');
        const user = localStorage.getItem('user');
        return !!(token && user);
    };
    
})();
